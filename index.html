<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Zeinab Arabnia Portfolio</title>
    <!-- Tailwind CSS CDN -->
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;700&display=swap" rel="stylesheet">
    <style>
        html {
            /* Smooth horizontal scrolling */
            scroll-behavior: smooth;
        }
        body {
            font-family: 'Inter', sans-serif;
            overflow-x: scroll; /* Enable horizontal scrolling */
            overflow-y: hidden; /* Disable vertical scrolling */
            background-color: #000;
        }
        .video-container {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            overflow: hidden;
            z-index: -1;
        }
        .video-container video {
            min-width: 100%;
            min-height: 100%;
            width: auto;
            height: auto;
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            object-fit: cover;
        }
        .content-section {
            width: 100vw; /* Each section takes up the full viewport width */
            min-width: 100vw;
            height: 100vh;
            padding: 8rem 2rem;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            flex-shrink: 0; /* Prevents sections from shrinking */
        }
    </style>
</head>
<body class="bg-black text-white">
    <div class="video-container">
        <!-- Replace the video source below with your own video file URL -->
        <video autoplay muted loop playsinline poster="https://placehold.co/1920x1080/000000/FFFFFF?text=Loading+video...">
            <!-- Placeholder video from Mixkit.co -->
            <source src="https://assets.mixkit.co/videos/preview/mixkit-black-and-white-abstract-4045-large.mp4" type="video/mp4">
            Your browser does not support the video tag.
        </video>
        <div class="absolute inset-0 bg-black opacity-30"></div>
    </div>

    <!-- Fixed top transparent navigation bar -->
    <header id="header" class="fixed top-0 left-0 w-full p-6 md:p-8 z-20 bg-transparent transition-colors duration-300">
        <nav class="flex justify-between items-center max-w-7xl mx-auto">
            <a href="#" id="title-link" class="text-white text-xl md:text-3xl font-bold tracking-wide transition-colors duration-300 hover:opacity-80">Zeinab Arabnia</a>
            <ul id="nav-links" class="flex space-x-4 md:space-x-8 text-sm md:text-base font-thin text-white transition-colors duration-300">
                <li><a href="#work" class="hover:opacity-80">Work</a></li>
                <li><a href="#projects" class="hover:opacity-80">Projects</a></li>
                <li><a href="#about" class="hover:opacity-80">About</a></li>
            </ul>
        </nav>
    </header>

    <!-- Main content container for horizontal scrolling -->
    <main class="relative z-10 flex w-fit h-full snap-x snap-mandatory">
        <!-- Hero section as the first horizontal "slide" -->
        <section id="hero-section" class="content-section text-center snap-center">
            <div class="p-8 bg-black bg-opacity-40 rounded-xl max-w-lg mx-4">
                <h1 class="text-3xl md:text-5xl font-bold mb-4 tracking-wider">CREATIVE PORTFOLIO</h1>
                <p class="text-sm md:text-lg opacity-80">A showcase of design, development, and artistic projects. Explore my work and get to know me.</p>
            </div>
        </section>

        <!-- Work section -->
        <section id="work" class="content-section bg-gray-900 bg-opacity-90 snap-center">
            <div class="max-w-4xl mx-auto text-center">
                <h2 class="text-4xl md:text-5xl font-bold mb-6">Work</h2>
                <p class="text-lg md:text-xl text-gray-300">This is where I'll showcase my professional work and client projects. It can include case studies, design prototypes, and development highlights.</p>
                <div class="mt-8 grid grid-cols-1 md:grid-cols-2 gap-8">
                    <div class="bg-white p-6 rounded-xl text-black">
                        <h3 class="font-bold text-xl">Project Alpha</h3>
                        <p class="text-sm mt-2">A user-friendly mobile application for task management.</p>
                    </div>
                    <div class="bg-white p-6 rounded-xl text-black">
                        <h3 class="font-bold text-xl">Project Beta</h3>
                        <p class="text-sm mt-2">A responsive e-commerce website for a local business.</p>
                    </div>
                </div>
            </div>
        </section>

        <!-- Projects section -->
        <section id="projects" class="content-section bg-gray-800 bg-opacity-90 snap-center">
            <div class="max-w-6xl w-full mx-auto text-center overflow-x-scroll no-scrollbar">
                <h2 class="text-4xl md:text-5xl font-bold mb-6">Projects</h2>
                <p class="text-lg md:text-xl text-gray-300 mb-10">Here you can find my personal and experimental projects. These are often where I explore new technologies and creative ideas.</p>
                <div class="flex space-x-8 px-4 py-2">
                    <!-- Project Card with Image -->
                    <div class="group relative flex-shrink-0 w-80 h-96 rounded-xl overflow-hidden shadow-lg transition-transform duration-300 ease-in-out hover:scale-105">
                        <img src="https://placehold.co/600x400/1e293b/FFFFFF?text=AI+Generated+Art" alt="AI Generated Art" class="w-full h-full object-cover">
                        <div class="absolute inset-0 bg-black bg-opacity-50 group-hover:bg-opacity-70 transition-colors duration-300"></div>
                        <div class="absolute inset-0 flex flex-col justify-end p-6 text-left">
                            <h3 class="text-white text-xl md:text-2xl font-bold mb-2">AI-Generated Art</h3>
                            <p class="text-gray-200 text-sm opacity-90">An exploration of art created with machine learning models.</p>
                        </div>
                    </div>
                    <!-- Project Card with Video -->
                    <div class="group relative flex-shrink-0 w-80 h-96 rounded-xl overflow-hidden shadow-lg transition-transform duration-300 ease-in-out hover:scale-105">
                        <video autoplay muted loop playsinline class="w-full h-full object-cover">
                            <source src="https://assets.mixkit.co/videos/preview/mixkit-flying-over-an-abstract-city-4933-large.mp4" type="video/mp4">
                            Your browser does not support the video tag.
                        </video>
                        <div class="absolute inset-0 bg-black bg-opacity-50 group-hover:bg-opacity-70 transition-colors duration-300"></div>
                        <div class="absolute inset-0 flex flex-col justify-end p-6 text-left">
                            <h3 class="text-white text-xl md:text-2xl font-bold mb-2">Motion Graphics</h3>
                            <p class="text-gray-200 text-sm opacity-90">An artistic animation exploring abstract cityscapes.</p>
                        </div>
                    </div>
                    <!-- Another Project Card with Image -->
                    <div class="group relative flex-shrink-0 w-80 h-96 rounded-xl overflow-hidden shadow-lg transition-transform duration-300 ease-in-out hover:scale-105">
                        <img src="https://placehold.co/600x400/314159/FFFFFF?text=Web+Development" alt="Web Development" class="w-full h-full object-cover">
                        <div class="absolute inset-0 bg-black bg-opacity-50 group-hover:bg-opacity-70 transition-colors duration-300"></div>
                        <div class="absolute inset-0 flex flex-col justify-end p-6 text-left">
                            <h3 class="text-white text-xl md:text-2xl font-bold mb-2">Responsive Web App</h3>
                            <p class="text-gray-200 text-sm opacity-90">A modern web application built with a responsive design.</p>
                        </div>
                    </div>
                    <!-- Project Card with Image -->
                    <div class="group relative flex-shrink-0 w-80 h-96 rounded-xl overflow-hidden shadow-lg transition-transform duration-300 ease-in-out hover:scale-105">
                        <img src="https://placehold.co/600x400/4c5c78/FFFFFF?text=Product+Design" alt="Product Design" class="w-full h-full object-cover">
                        <div class="absolute inset-0 bg-black bg-opacity-50 group-hover:bg-opacity-70 transition-colors duration-300"></div>
                        <div class="absolute inset-0 flex flex-col justify-end p-6 text-left">
                            <h3 class="text-white text-xl md:text-2xl font-bold mb-2">Product Design</h3>
                            <p class="text-gray-200 text-sm opacity-90">A UI/UX case study for a new mobile product.</p>
                        </div>
                    </div>
                    <!-- Project Card with Video -->
                    <div class="group relative flex-shrink-0 w-80 h-96 rounded-xl overflow-hidden shadow-lg transition-transform duration-300 ease-in-out hover:scale-105">
                        <video autoplay muted loop playsinline class="w-full h-full object-cover">
                            <source src="https://assets.mixkit.co/videos/preview/mixkit-abstract-cubes-4648-large.mp4" type="video/mp4">
                            Your browser does not support the video tag.
                        </video>
                        <div class="absolute inset-0 bg-black bg-opacity-50 group-hover:bg-opacity-70 transition-colors duration-300"></div>
                        <div class="absolute inset-0 flex flex-col justify-end p-6 text-left">
                            <h3 class="text-white text-xl md:text-2xl font-bold mb-2">3D Modeling</h3>
                            <p class="text-gray-200 text-sm opacity-90">Exploring the use of 3D modeling for artistic expression.</p>
                        </div>
                    </div>
                    <!-- Another Project Card with Image -->
                    <div class="group relative flex-shrink-0 w-80 h-96 rounded-xl overflow-hidden shadow-lg transition-transform duration-300 ease-in-out hover:scale-105">
                        <img src="https://placehold.co/600x400/6b7280/FFFFFF?text=Illustrations" alt="Illustrations" class="w-full h-full object-cover">
                        <div class="absolute inset-0 bg-black bg-opacity-50 group-hover:bg-opacity-70 transition-colors duration-300"></div>
                        <div class="absolute inset-0 flex flex-col justify-end p-6 text-left">
                            <h3 class="text-white text-xl md:text-2xl font-bold mb-2">Illustrations</h3>
                            <p class="text-gray-200 text-sm opacity-90">A collection of digital illustrations and sketches.</p>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- About section -->
        <section id="about" class="content-section bg-gray-700 bg-opacity-90 snap-center">
            <div class="max-w-4xl mx-auto text-center">
                <h2 class="text-4xl md:text-5xl font-bold mb-6">About</h2>
                <p class="text-lg md:text-xl text-gray-300">Hello! I'm Zeinab Arabnia, a passionate designer and developer. I believe in creating beautiful and functional experiences. I am always learning new things and enjoy working on challenging projects.</p>
            </div>
        </section>
    </main>

    <script>
        // Ensure the video plays on mobile devices
        window.addEventListener('load', () => {
            const video = document.querySelector('video');
            if (video) {
                video.play().catch(error => {
                    console.log("Video autoplay failed, user interaction required:", error);
                });
            }
        });

        // Intersection Observer to change header style on horizontal scroll
        document.addEventListener('DOMContentLoaded', () => {
            const header = document.getElementById('header');
            const navLinks = document.getElementById('nav-links');
            const titleLink = document.getElementById('title-link');
            const sections = document.querySelectorAll('.content-section');

            const observerOptions = {
                root: null,
                rootMargin: '0px',
                threshold: 0.5 // Trigger when 50% of the section is visible
            };

            const observerCallback = (entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        const currentSectionId = entry.target.id;
                        if (currentSectionId === 'hero-section') {
                            // On the video section, use white text and transparent background
                            header.classList.remove('bg-white');
                            header.classList.add('bg-transparent');
                            navLinks.classList.remove('text-black');
                            navLinks.classList.add('text-white');
                            titleLink.classList.remove('text-black');
                            titleLink.classList.add('text-white');
                        } else {
                            // On any other section, use black text and a white background
                            header.classList.remove('bg-transparent');
                            header.classList.add('bg-white');
                            navLinks.classList.remove('text-white');
                            navLinks.classList.add('text-black');
                            titleLink.classList.remove('text-white');
                            titleLink.classList.add('text-black');
                        }
                    }
                });
            };

            const observer = new IntersectionObserver(observerCallback, observerOptions);

            sections.forEach(section => observer.observe(section));
        });
    </script>
</body>
</html>
